/* VYPX - Animation Styles */

/* Page Transitions */
.page-enter {
    opacity: 0;
    transform: translateX(100px);
}

.page-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-exit {
    opacity: 1;
    transform: translateX(0);
}

.page-exit-active {
    opacity: 0;
    transform: translateX(-100px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Neon Glow Effects */
.neon-glow {
    position: relative;
    overflow: hidden;
}

.neon-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--golden-accent), #FFA500, var(--golden-accent));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.neon-glow:hover::before {
    opacity: 1;
    animation: neon-pulse 2s ease-in-out infinite;
}

@keyframes neon-pulse {
    0%, 100% {
        box-shadow: 0 0 5px var(--golden-accent),
                    0 0 10px var(--golden-accent),
                    0 0 15px var(--golden-accent);
    }
    50% {
        box-shadow: 0 0 10px var(--golden-accent),
                    0 0 20px var(--golden-accent),
                    0 0 30px var(--golden-accent);
    }
}

/* Floating Animation */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Fade In Up Animation */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger Animation for Lists */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: staggerFadeIn 0.5s ease forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes staggerFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Shimmer Effect */
.shimmer {
    position: relative;
    overflow: hidden;
}

.shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Typing Animation */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 10px 15px;
    background: var(--glass-bg);
    border-radius: 20px;
    width: fit-content;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: var(--golden-accent);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
    0%, 60%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Hover Scale Effect */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Bounce Animation */
.bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Slide In Animations */
.slide-in-left {
    animation: slideInLeft 0.5s ease forwards;
}

.slide-in-right {
    animation: slideInRight 0.5s ease forwards;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Notification Badge Animation */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff3040;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Loading Skeleton */
.skeleton {
    background: linear-gradient(90deg, var(--glass-bg) 25%, rgba(255, 255, 255, 0.1) 50%, var(--glass-bg) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Heart Beat Animation for Likes */
.heart-beat {
    animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
    0%, 100% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.2);
    }
    50% {
        transform: scale(1.1);
    }
    75% {
        transform: scale(1.15);
    }
}

/* Smooth Scroll */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* Parallax Effect */
.parallax {
    transform: translateZ(0);
    transition: transform 0.1s ease-out;
}

/* Glassmorphism Hover Effect */
.glass-hover {
    transition: all 0.3s ease;
}

.glass-hover:hover {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(25px);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.2);
}
