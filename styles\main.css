/* VYPX - Main Styles */

:root {
    --bg-primary: #0A0A0A;
    --bg-secondary: #1A1A1A;
    --golden-accent: #FFD700;
    --golden-glow: rgba(255, 215, 0, 0.3);
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --text-primary: rgba(255, 255, 255, 0.85);
    --text-secondary: rgba(255, 255, 255, 0.6);
    --text-muted: rgba(255, 255, 255, 0.4);
    
    --font-primary: 'Poppins', sans-serif;
    --font-heading: '<PERSON><PERSON> Neue', cursive;
    
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    line-height: 1.6;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.golden-ring {
    width: 80px;
    height: 80px;
    border: 3px solid transparent;
    border-top: 3px solid var(--golden-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
    position: relative;
}

.golden-ring::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: conic-gradient(transparent, var(--golden-glow));
    z-index: -1;
    animation: pulse 2s ease-in-out infinite;
}

.loading-text {
    font-family: var(--font-heading);
    font-size: 2rem;
    color: var(--golden-accent);
    letter-spacing: 3px;
    text-shadow: 0 0 20px var(--golden-glow);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* App Container */
.app-container {
    min-height: 100vh;
    position: relative;
}

/* Page System */
.page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.page.active {
    opacity: 1;
    visibility: visible;
}

/* Glass Card Effect */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--golden-accent), transparent);
    opacity: 0.5;
}

/* Golden Button */
.golden-btn {
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    border: none;
    border-radius: var(--border-radius);
    padding: 15px 30px;
    font-family: var(--font-primary);
    font-weight: 600;
    font-size: 1rem;
    color: var(--bg-primary);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.golden-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px var(--golden-glow);
}

.golden-btn .btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.golden-btn:hover .btn-glow {
    left: 100%;
}

/* Input Styles */
.input-group {
    position: relative;
    margin-bottom: 20px;
}

.input-group i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--golden-accent);
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.input-group input:focus {
    outline: none;
    border-color: var(--golden-accent);
    box-shadow: 0 0 20px var(--golden-glow);
    background: rgba(255, 255, 255, 0.08);
}

.input-group input::placeholder {
    color: var(--text-muted);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: normal;
    letter-spacing: 2px;
}

.logo {
    font-family: var(--font-heading);
    font-size: 3rem;
    color: var(--golden-accent);
    text-shadow: 0 0 30px var(--golden-glow);
    letter-spacing: 5px;
    margin-bottom: 10px;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--golden-accent);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #FFA500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .logo {
        font-size: 2rem;
    }
    
    .golden-btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}
