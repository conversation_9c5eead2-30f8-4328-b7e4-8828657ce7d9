// VYPX - Feed Module

class FeedManager {
    constructor() {
        this.posts = [];
        this.stories = [];
        this.currentPostIndex = 0;
        this.isLoading = false;
        this.init();
    }

    init() {
        this.setupInfiniteScroll();
        this.setupPostInteractions();
    }

    setupInfiniteScroll() {
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) return;

        mainContent.addEventListener('scroll', () => {
            const { scrollTop, scrollHeight, clientHeight } = mainContent;
            
            if (scrollTop + clientHeight >= scrollHeight - 100 && !this.isLoading) {
                this.loadMorePosts();
            }
        });
    }

    setupPostInteractions() {
        document.addEventListener('click', (e) => {
            // Like button
            if (e.target.closest('.like-btn')) {
                this.handleLike(e.target.closest('.like-btn'));
            }
            
            // Comment button
            if (e.target.closest('.comment-btn')) {
                this.handleComment(e.target.closest('.comment-btn'));
            }
            
            // Share button
            if (e.target.closest('.share-btn')) {
                this.handleShare(e.target.closest('.share-btn'));
            }
            
            // Save button
            if (e.target.closest('.save-btn')) {
                this.handleSave(e.target.closest('.save-btn'));
            }
        });
    }

    async loadMorePosts() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingSkeleton();
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const newPosts = this.generatePosts(3);
        this.posts.push(...newPosts);
        
        this.renderPosts(newPosts);
        this.hideLoadingSkeleton();
        
        this.isLoading = false;
    }

    generatePosts(count) {
        const users = [
            { name: 'CyberVibe', avatar: 'C', verified: true },
            { name: 'NeonDreamer', avatar: 'N', verified: false },
            { name: 'GoldenSoul', avatar: 'G', verified: true },
            { name: 'DarkArtist', avatar: 'D', verified: false },
            { name: 'VybeCheck', avatar: 'V', verified: true },
            { name: 'LunarGlow', avatar: 'L', verified: false },
            { name: 'PixelMaster', avatar: 'P', verified: true },
            { name: 'NightOwl', avatar: 'N', verified: false }
        ];

        const images = [
            'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=500&h=400&fit=crop',
            'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=500&h=400&fit=crop',
            'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=500&h=400&fit=crop',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=400&fit=crop',
            'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=500&h=400&fit=crop',
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=400&fit=crop'
        ];

        const captions = [
            'Living in the golden hour ✨ #VYPXVibes #NeonLife',
            'Midnight creativity hits different 🌙 #DarkArt #VYPX',
            'Chase the light, embrace the vibe ⚡ #GoldenHour #VYPXLife',
            'Neon dreams and digital schemes 💫 #CyberVibes #VYPX',
            'When the city sleeps, we create 🌃 #NightVibes #VYPXArt',
            'Golden moments in a digital world 🔥 #VYPXMoments #Aesthetic'
        ];

        const posts = [];
        
        for (let i = 0; i < count; i++) {
            const user = users[Math.floor(Math.random() * users.length)];
            const post = {
                id: Date.now() + i,
                user: user.name,
                avatar: user.avatar,
                verified: user.verified,
                time: this.getRandomTime(),
                image: images[Math.floor(Math.random() * images.length)],
                caption: captions[Math.floor(Math.random() * captions.length)],
                likes: Math.floor(Math.random() * 5000) + 100,
                comments: Math.floor(Math.random() * 200) + 10,
                isLiked: false,
                isSaved: false
            };
            posts.push(post);
        }
        
        return posts;
    }

    getRandomTime() {
        const times = ['1m', '5m', '15m', '30m', '1h', '2h', '3h', '5h', '8h', '12h', '1d', '2d'];
        return times[Math.floor(Math.random() * times.length)];
    }

    renderPosts(posts) {
        const postsContainer = document.getElementById('posts-feed');
        
        posts.forEach((post, index) => {
            const postEl = this.createPostElement(post);
            postEl.style.opacity = '0';
            postEl.style.transform = 'translateY(30px)';
            
            postsContainer.appendChild(postEl);
            
            // Animate post appearance
            gsap.to(postEl, {
                opacity: 1,
                y: 0,
                duration: 0.6,
                delay: index * 0.1,
                ease: "power2.out"
            });
        });
    }

    createPostElement(post) {
        const postEl = document.createElement('div');
        postEl.className = 'post-card glass-hover';
        postEl.dataset.postId = post.id;
        
        postEl.innerHTML = `
            <div class="post-header">
                <div class="post-avatar">${post.avatar}</div>
                <div class="post-user-info">
                    <h4>
                        ${post.user}
                        ${post.verified ? '<i class="fas fa-check-circle" style="color: var(--golden-accent); font-size: 0.8rem; margin-left: 5px;"></i>' : ''}
                    </h4>
                    <span>${post.time} ago</span>
                </div>
                <div class="post-options">
                    <i class="fas fa-ellipsis-h"></i>
                </div>
            </div>
            <div class="post-content">
                <img src="${post.image}" alt="Post" class="post-image" loading="lazy">
            </div>
            <div class="post-actions">
                <button class="action-btn like-btn" data-post-id="${post.id}">
                    <i class="${post.isLiked ? 'fas' : 'far'} fa-heart"></i>
                </button>
                <button class="action-btn comment-btn" data-post-id="${post.id}">
                    <i class="far fa-comment"></i>
                </button>
                <button class="action-btn share-btn" data-post-id="${post.id}">
                    <i class="far fa-paper-plane"></i>
                </button>
                <button class="action-btn save-btn" data-post-id="${post.id}" style="margin-left: auto;">
                    <i class="${post.isSaved ? 'fas' : 'far'} fa-bookmark"></i>
                </button>
            </div>
            <div class="post-stats">
                <span class="likes-count">${this.formatNumber(post.likes)} likes</span>
                <span class="comments-count">${this.formatNumber(post.comments)} comments</span>
            </div>
            <div class="post-caption">
                <span class="username">${post.user}</span> ${post.caption}
            </div>
        `;
        
        return postEl;
    }

    handleLike(button) {
        const postId = button.dataset.postId;
        const post = this.posts.find(p => p.id == postId);
        const icon = button.querySelector('i');
        const likesCount = button.closest('.post-card').querySelector('.likes-count');
        
        if (!post) return;
        
        post.isLiked = !post.isLiked;
        
        if (post.isLiked) {
            post.likes++;
            icon.classList.remove('far');
            icon.classList.add('fas');
            button.classList.add('liked');
            
            // Heart animation
            button.classList.add('heart-beat');
            setTimeout(() => button.classList.remove('heart-beat'), 600);
            
            // Create floating heart
            this.createFloatingHeart(button);
            
        } else {
            post.likes--;
            icon.classList.remove('fas');
            icon.classList.add('far');
            button.classList.remove('liked');
        }
        
        likesCount.textContent = `${this.formatNumber(post.likes)} likes`;
        
        // Animate likes count
        gsap.fromTo(likesCount, 
            { scale: 1.2, color: '#FFD700' },
            { scale: 1, color: 'var(--text-primary)', duration: 0.3 }
        );
    }

    createFloatingHeart(button) {
        const heart = document.createElement('div');
        heart.innerHTML = '<i class="fas fa-heart"></i>';
        heart.style.cssText = `
            position: absolute;
            color: #ff3040;
            font-size: 1.5rem;
            pointer-events: none;
            z-index: 1000;
        `;
        
        const rect = button.getBoundingClientRect();
        heart.style.left = rect.left + rect.width / 2 + 'px';
        heart.style.top = rect.top + rect.height / 2 + 'px';
        
        document.body.appendChild(heart);
        
        gsap.to(heart, {
            y: -50,
            opacity: 0,
            scale: 1.5,
            duration: 1,
            ease: "power2.out",
            onComplete: () => heart.remove()
        });
    }

    handleComment(button) {
        // Animate comment button
        gsap.to(button, {
            scale: 1.2,
            duration: 0.1,
            yoyo: true,
            repeat: 1
        });
        
        // TODO: Open comment modal
        console.log('Comment functionality - to be implemented');
    }

    handleShare(button) {
        // Animate share button
        gsap.to(button, {
            rotation: 360,
            duration: 0.5,
            ease: "power2.out"
        });
        
        // TODO: Open share modal
        console.log('Share functionality - to be implemented');
    }

    handleSave(button) {
        const postId = button.dataset.postId;
        const post = this.posts.find(p => p.id == postId);
        const icon = button.querySelector('i');
        
        if (!post) return;
        
        post.isSaved = !post.isSaved;
        
        if (post.isSaved) {
            icon.classList.remove('far');
            icon.classList.add('fas');
            
            // Animate save
            gsap.to(button, {
                scale: 1.2,
                color: '#FFD700',
                duration: 0.2,
                yoyo: true,
                repeat: 1
            });
        } else {
            icon.classList.remove('fas');
            icon.classList.add('far');
        }
    }

    showLoadingSkeleton() {
        const postsContainer = document.getElementById('posts-feed');
        const skeleton = document.createElement('div');
        skeleton.className = 'loading-skeleton';
        skeleton.innerHTML = `
            <div class="skeleton-post">
                <div class="skeleton-header">
                    <div class="skeleton-avatar skeleton"></div>
                    <div class="skeleton-user-info">
                        <div class="skeleton-line skeleton"></div>
                        <div class="skeleton-line skeleton" style="width: 60%;"></div>
                    </div>
                </div>
                <div class="skeleton-image skeleton"></div>
                <div class="skeleton-actions">
                    <div class="skeleton-action skeleton"></div>
                    <div class="skeleton-action skeleton"></div>
                    <div class="skeleton-action skeleton"></div>
                </div>
            </div>
        `;
        
        postsContainer.appendChild(skeleton);
    }

    hideLoadingSkeleton() {
        const skeleton = document.querySelector('.loading-skeleton');
        if (skeleton) {
            gsap.to(skeleton, {
                opacity: 0,
                duration: 0.3,
                onComplete: () => skeleton.remove()
            });
        }
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
}

// Initialize feed manager
document.addEventListener('DOMContentLoaded', () => {
    window.feedManager = new FeedManager();
});
