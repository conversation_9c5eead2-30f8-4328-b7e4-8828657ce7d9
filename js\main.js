// VYPX - Main JavaScript

class VYPXApp {
    constructor() {
        this.currentPage = 'auth';
        this.isLoggedIn = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.showLoadingScreen();
        this.initializeAnimations();
    }

    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        
        // Simulate loading time
        setTimeout(() => {
            gsap.to(loadingScreen, {
                opacity: 0,
                duration: 0.5,
                onComplete: () => {
                    loadingScreen.style.display = 'none';
                    this.showPage('auth');
                }
            });
        }, 2000);
    }

    setupEventListeners() {
        // Navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-page]')) {
                e.preventDefault();
                const page = e.target.getAttribute('data-page');
                this.navigateToPage(page);
            }
        });

        // Auth tabs
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-tab]')) {
                this.switchAuthTab(e.target.getAttribute('data-tab'));
            }
        });

        // Form submissions
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('signup-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSignup();
        });

        // Scroll animations
        this.setupScrollAnimations();
    }

    switchAuthTab(tab) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

        // Update forms
        document.querySelectorAll('.auth-form').forEach(form => {
            form.classList.remove('active');
        });
        document.getElementById(`${tab}-form`).classList.add('active');

        // Animate form transition
        gsap.fromTo(`#${tab}-form`, 
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.3 }
        );
    }

    async handleLogin() {
        const form = document.getElementById('login-form');
        const formData = new FormData(form);

        const credentials = {
            username: form.querySelector('input[type="text"]').value,
            password: form.querySelector('input[type="password"]').value
        };

        if (window.authManager) {
            const result = await window.authManager.login(credentials);
            if (result.success) {
                this.isLoggedIn = true;
                this.navigateToPage('home');
                this.loadFeedData();
            }
        }
    }

    async handleSignup() {
        const form = document.getElementById('signup-form');

        const userData = {
            fullName: form.querySelector('input[placeholder="Full Name"]').value,
            email: form.querySelector('input[type="email"]').value,
            username: form.querySelector('input[placeholder="Username"]').value,
            password: form.querySelector('input[type="password"]').value
        };

        if (window.authManager) {
            const result = await window.authManager.signup(userData);
            if (result.success) {
                this.isLoggedIn = true;
                this.navigateToPage('home');
                this.loadFeedData();
            }
        }
    }

    navigateToPage(pageName) {
        if (!this.isLoggedIn && pageName !== 'auth') {
            return;
        }

        const currentPageEl = document.querySelector('.page.active');
        const newPageEl = document.getElementById(`${pageName}-page`);

        if (!newPageEl) return;

        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const navItem = document.querySelector(`[data-page="${pageName}"]`);
        if (navItem) navItem.classList.add('active');

        // Animate page transition
        gsap.timeline()
            .to(currentPageEl, {
                opacity: 0,
                x: -100,
                duration: 0.3,
                onComplete: () => {
                    currentPageEl.classList.remove('active');
                }
            })
            .set(newPageEl, { x: 100, opacity: 0 })
            .to(newPageEl, {
                opacity: 1,
                x: 0,
                duration: 0.3,
                onStart: () => {
                    newPageEl.classList.add('active');
                }
            });

        this.currentPage = pageName;
        
        // Load page-specific content
        this.loadPageContent(pageName);
    }

    showPage(pageName) {
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });
        document.getElementById(`${pageName}-page`).classList.add('active');
    }

    loadPageContent(pageName) {
        switch(pageName) {
            case 'home':
                this.loadFeedData();
                break;
            case 'explore':
                this.loadExplorePage();
                break;
            case 'messages':
                this.loadMessagesPage();
                break;
            case 'notifications':
                this.loadNotificationsPage();
                break;
            case 'create':
                this.loadCreatePage();
                break;
            case 'profile':
                this.loadProfilePage();
                break;
        }
    }

    loadExplorePage() {
        this.loadExploreGrid();
        this.setupExploreFilters();
    }

    loadExploreGrid() {
        const exploreGrid = document.getElementById('explore-grid');
        if (!exploreGrid) return;

        const exploreItems = [
            { image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=300&fit=crop', likes: 1247, comments: 89 },
            { image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop', likes: 892, comments: 45 },
            { image: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=300&h=300&fit=crop', likes: 2156, comments: 134 },
            { image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop', likes: 756, comments: 67 },
            { image: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=300&h=300&fit=crop', likes: 1834, comments: 92 },
            { image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=300&fit=crop', likes: 945, comments: 78 }
        ];

        exploreGrid.innerHTML = '';
        exploreItems.forEach((item, index) => {
            const exploreItem = document.createElement('div');
            exploreItem.className = 'explore-item fade-in-up';
            exploreItem.style.animationDelay = `${index * 0.1}s`;
            exploreItem.innerHTML = `
                <img src="${item.image}" alt="Explore post" loading="lazy">
                <div class="explore-overlay">
                    <div class="explore-stats">
                        <span><i class="fas fa-heart"></i> ${this.formatNumber(item.likes)}</span>
                        <span><i class="fas fa-comment"></i> ${this.formatNumber(item.comments)}</span>
                    </div>
                </div>
            `;
            exploreGrid.appendChild(exploreItem);
        });
    }

    setupExploreFilters() {
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                // Animate filter change
                gsap.to('.explore-grid', {
                    opacity: 0.5,
                    duration: 0.2,
                    onComplete: () => {
                        // Here you would filter the content
                        gsap.to('.explore-grid', { opacity: 1, duration: 0.2 });
                    }
                });
            });
        });
    }

    loadMessagesPage() {
        this.loadChatList();
        this.setupMessageInput();
    }

    loadChatList() {
        const chatList = document.getElementById('chat-list');
        if (!chatList) return;

        const chats = [
            { name: 'NeonVibes', avatar: 'N', preview: 'Hey! Love your latest post ✨', time: '2m', unread: 2, online: true },
            { name: 'DarkArtist', avatar: 'D', preview: 'Thanks for the follow!', time: '1h', unread: 0, online: false },
            { name: 'GoldenSoul', avatar: 'G', preview: 'That sunset shot is incredible', time: '3h', unread: 1, online: true },
            { name: 'CyberQueen', avatar: 'C', preview: 'Let\'s collaborate on something', time: '1d', unread: 0, online: false }
        ];

        chatList.innerHTML = '';
        chats.forEach((chat, index) => {
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-item stagger-item';
            chatItem.innerHTML = `
                <div class="chat-avatar ${chat.online ? 'online' : ''}">${chat.avatar}</div>
                <div class="chat-info">
                    <h4>${chat.name}</h4>
                    <div class="chat-preview">${chat.preview}</div>
                </div>
                <div class="chat-meta">
                    <div>${chat.time}</div>
                    ${chat.unread > 0 ? `<div class="unread-badge">${chat.unread}</div>` : ''}
                </div>
            `;

            chatItem.addEventListener('click', () => {
                document.querySelectorAll('.chat-item').forEach(item => item.classList.remove('active'));
                chatItem.classList.add('active');
                this.openConversation(chat);
            });

            chatList.appendChild(chatItem);
        });
    }

    openConversation(chat) {
        document.getElementById('chat-welcome').style.display = 'none';
        document.getElementById('chat-conversation').style.display = 'flex';

        // Load messages for this chat
        this.loadMessages(chat);
    }

    loadMessages(chat) {
        const messagesContainer = document.getElementById('messages-container');
        const messages = [
            { text: 'Hey! How are you doing?', sent: false, time: '10:30 AM' },
            { text: 'I\'m great! Just posted a new photo', sent: true, time: '10:32 AM' },
            { text: 'Love your latest post ✨', sent: false, time: '10:35 AM' },
            { text: 'Thank you! Means a lot 🙏', sent: true, time: '10:36 AM' }
        ];

        messagesContainer.innerHTML = '';
        messages.forEach(message => {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${message.sent ? 'sent' : ''}`;
            messageEl.innerHTML = `
                <div class="message-avatar">${message.sent ? 'Y' : chat.avatar}</div>
                <div>
                    <div class="message-content">${message.text}</div>
                    <div class="message-time">${message.time}</div>
                </div>
            `;
            messagesContainer.appendChild(messageEl);
        });

        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    setupMessageInput() {
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.querySelector('.send-btn');

        if (messageInput && sendBtn) {
            const sendMessage = () => {
                const text = messageInput.value.trim();
                if (text) {
                    // Add message to conversation
                    const messagesContainer = document.getElementById('messages-container');
                    const messageEl = document.createElement('div');
                    messageEl.className = 'message sent';
                    messageEl.innerHTML = `
                        <div class="message-avatar">Y</div>
                        <div>
                            <div class="message-content">${text}</div>
                            <div class="message-time">Now</div>
                        </div>
                    `;
                    messagesContainer.appendChild(messageEl);
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;

                    messageInput.value = '';

                    // Animate new message
                    gsap.fromTo(messageEl,
                        { opacity: 0, y: 20 },
                        { opacity: 1, y: 0, duration: 0.3 }
                    );
                }
            };

            sendBtn.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        }
    }

    loadNotificationsPage() {
        this.loadNotifications();
        this.setupNotificationFilters();
    }

    loadNotifications() {
        const notificationsList = document.getElementById('notifications-list');
        if (!notificationsList) return;

        const notifications = [
            {
                type: 'like',
                user: 'NeonVibes',
                avatar: 'N',
                text: 'liked your post',
                time: '2m ago',
                unread: true,
                media: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=50&h=50&fit=crop'
            },
            {
                type: 'comment',
                user: 'DarkArtist',
                avatar: 'D',
                text: 'commented on your post: "Amazing work! 🔥"',
                time: '15m ago',
                unread: true,
                media: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=50&h=50&fit=crop'
            },
            {
                type: 'follow',
                user: 'GoldenSoul',
                avatar: 'G',
                text: 'started following you',
                time: '1h ago',
                unread: false
            },
            {
                type: 'like',
                user: 'CyberQueen',
                avatar: 'C',
                text: 'liked your post',
                time: '3h ago',
                unread: false,
                media: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=50&h=50&fit=crop'
            }
        ];

        notificationsList.innerHTML = '';
        notifications.forEach((notification, index) => {
            const notificationEl = document.createElement('div');
            notificationEl.className = `notification-item ${notification.unread ? 'unread' : ''} stagger-item`;
            notificationEl.innerHTML = `
                <div class="notification-avatar">
                    ${notification.avatar}
                    <div class="notification-icon ${notification.type}">
                        <i class="fas fa-${notification.type === 'like' ? 'heart' : notification.type === 'comment' ? 'comment' : 'user-plus'}"></i>
                    </div>
                </div>
                <div class="notification-content">
                    <div class="notification-text">
                        <span class="username">${notification.user}</span> ${notification.text}
                    </div>
                    <div class="notification-time">${notification.time}</div>
                </div>
                ${notification.media ? `<img src="${notification.media}" alt="Post" class="notification-media">` : ''}
            `;
            notificationsList.appendChild(notificationEl);
        });
    }

    setupNotificationFilters() {
        document.querySelectorAll('.notification-filters .filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.notification-filters .filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                // Filter notifications based on type
                const filter = e.target.dataset.filter;
                this.filterNotifications(filter);
            });
        });
    }

    filterNotifications(filter) {
        const notifications = document.querySelectorAll('.notification-item');
        notifications.forEach(notification => {
            if (filter === 'all') {
                notification.style.display = 'flex';
            } else {
                const hasFilterClass = notification.querySelector(`.notification-icon.${filter}`);
                notification.style.display = hasFilterClass ? 'flex' : 'none';
            }
        });
    }

    loadCreatePage() {
        this.setupFileUpload();
        this.setupCaptionCounter();
        this.setupPublishButton();
    }

    setupFileUpload() {
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const mediaPreview = document.getElementById('media-preview');

        if (!uploadArea || !fileInput) return;

        uploadArea.addEventListener('click', () => fileInput.click());

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#FFA500';
            uploadArea.style.background = 'rgba(255, 215, 0, 0.1)';
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = 'var(--golden-accent)';
            uploadArea.style.background = 'transparent';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            const files = e.dataTransfer.files;
            this.handleFileUpload(files);
        });

        fileInput.addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });
    }

    handleFileUpload(files) {
        if (files.length === 0) return;

        const file = files[0];
        const uploadArea = document.getElementById('upload-area');
        const mediaPreview = document.getElementById('media-preview');
        const previewContainer = mediaPreview.querySelector('.preview-container');

        if (file.type.startsWith('image/')) {
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            img.className = 'preview-item';
            img.onload = () => URL.revokeObjectURL(img.src);

            previewContainer.innerHTML = '';
            previewContainer.appendChild(img);

            uploadArea.style.display = 'none';
            mediaPreview.style.display = 'block';

            // Animate preview
            gsap.fromTo(mediaPreview,
                { opacity: 0, scale: 0.9 },
                { opacity: 1, scale: 1, duration: 0.5 }
            );
        }

        // Setup change media button
        const changeBtn = mediaPreview.querySelector('.change-media-btn');
        changeBtn.addEventListener('click', () => {
            uploadArea.style.display = 'block';
            mediaPreview.style.display = 'none';
            document.getElementById('file-input').value = '';
        });
    }

    setupCaptionCounter() {
        const captionTextarea = document.getElementById('post-caption');
        const charCount = document.getElementById('char-count');

        if (captionTextarea && charCount) {
            captionTextarea.addEventListener('input', () => {
                const count = captionTextarea.value.length;
                charCount.textContent = count;

                if (count > 2000) {
                    charCount.style.color = '#FF5252';
                } else if (count > 1800) {
                    charCount.style.color = '#FFA726';
                } else {
                    charCount.style.color = 'var(--text-muted)';
                }
            });
        }
    }

    setupPublishButton() {
        const publishBtn = document.getElementById('publish-btn');
        if (publishBtn) {
            publishBtn.addEventListener('click', () => {
                // Simulate post publishing
                publishBtn.disabled = true;
                publishBtn.querySelector('span').textContent = 'Publishing...';

                setTimeout(() => {
                    if (window.animationManager) {
                        window.animationManager.showNotification('Post published successfully!', 'success');
                    }
                    this.navigateToPage('home');
                }, 2000);
            });
        }
    }

    loadProfilePage() {
        this.loadProfilePosts();
        this.setupProfileTabs();
    }

    loadProfilePosts() {
        const profileGrid = document.getElementById('profile-grid');
        if (!profileGrid) return;

        const posts = [
            { image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=300&fit=crop', likes: 1247, comments: 89 },
            { image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop', likes: 892, comments: 45 },
            { image: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=300&h=300&fit=crop', likes: 2156, comments: 134 },
            { image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=300&fit=crop', likes: 756, comments: 67 },
            { image: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=300&h=300&fit=crop', likes: 1834, comments: 92 },
            { image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=300&h=300&fit=crop', likes: 945, comments: 78 }
        ];

        profileGrid.innerHTML = '';
        posts.forEach((post, index) => {
            const postEl = document.createElement('div');
            postEl.className = 'profile-post fade-in-up';
            postEl.style.animationDelay = `${index * 0.1}s`;
            postEl.innerHTML = `
                <img src="${post.image}" alt="Profile post" loading="lazy">
                <div class="profile-post-overlay">
                    <div class="profile-post-stat">
                        <i class="fas fa-heart"></i>
                        <span>${this.formatNumber(post.likes)}</span>
                    </div>
                    <div class="profile-post-stat">
                        <i class="fas fa-comment"></i>
                        <span>${this.formatNumber(post.comments)}</span>
                    </div>
                </div>
            `;
            profileGrid.appendChild(postEl);
        });
    }

    setupProfileTabs() {
        document.querySelectorAll('.profile-tabs .tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.profile-tabs .tab-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                const tab = e.target.dataset.tab;
                this.switchProfileTab(tab);
            });
        });
    }

    switchProfileTab(tab) {
        const profileGrid = document.getElementById('profile-grid');

        // Animate tab switch
        gsap.to(profileGrid, {
            opacity: 0.3,
            duration: 0.2,
            onComplete: () => {
                // Here you would load different content based on tab
                // For now, we'll just reload the same posts
                this.loadProfilePosts();
                gsap.to(profileGrid, { opacity: 1, duration: 0.2 });
            }
        });
    }

    loadFeedData() {
        this.loadStories();
        this.loadPosts();
        this.loadTrending();
        this.loadVIPUsers();
    }

    loadStories() {
        const storiesSection = document.querySelector('.stories-section');
        const stories = [
            { name: 'Alex', avatar: 'A', color: '#FF6B6B' },
            { name: 'Maya', avatar: 'M', color: '#4ECDC4' },
            { name: 'Jake', avatar: 'J', color: '#45B7D1' },
            { name: 'Luna', avatar: 'L', color: '#96CEB4' },
            { name: 'Zara', avatar: 'Z', color: '#FFEAA7' }
        ];

        stories.forEach((story, index) => {
            const storyEl = document.createElement('div');
            storyEl.className = 'story-item stagger-item';
            storyEl.innerHTML = `
                <div class="story-avatar" style="background: linear-gradient(135deg, ${story.color}, #FFD700)">
                    ${story.avatar}
                </div>
                <span>${story.name}</span>
            `;
            storiesSection.appendChild(storyEl);
        });
    }

    loadPosts() {
        const postsContainer = document.getElementById('posts-feed');
        const posts = [
            {
                user: 'NeonVibes',
                avatar: 'N',
                time: '2h',
                image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=500&h=400&fit=crop',
                caption: 'Living in the golden hour ✨ #VYPXVibes #NeonLife',
                likes: 1247,
                comments: 89
            },
            {
                user: 'DarkArtist',
                avatar: 'D',
                time: '4h',
                image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=500&h=400&fit=crop',
                caption: 'Midnight creativity hits different 🌙 #DarkArt #VYPX',
                likes: 892,
                comments: 45
            },
            {
                user: 'GoldenSoul',
                avatar: 'G',
                time: '6h',
                image: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=500&h=400&fit=crop',
                caption: 'Chase the light, embrace the vibe ⚡ #GoldenHour #VYPXLife',
                likes: 2156,
                comments: 134
            }
        ];

        posts.forEach((post, index) => {
            const postEl = document.createElement('div');
            postEl.className = 'post-card fade-in-up';
            postEl.style.animationDelay = `${index * 0.2}s`;
            postEl.innerHTML = `
                <div class="post-header">
                    <div class="post-avatar">${post.avatar}</div>
                    <div class="post-user-info">
                        <h4>${post.user}</h4>
                        <span>${post.time} ago</span>
                    </div>
                    <div class="post-options">
                        <i class="fas fa-ellipsis-h"></i>
                    </div>
                </div>
                <div class="post-content">
                    <img src="${post.image}" alt="Post" class="post-image">
                </div>
                <div class="post-actions">
                    <button class="action-btn like-btn">
                        <i class="far fa-heart"></i>
                    </button>
                    <button class="action-btn">
                        <i class="far fa-comment"></i>
                    </button>
                    <button class="action-btn">
                        <i class="far fa-paper-plane"></i>
                    </button>
                    <button class="action-btn" style="margin-left: auto;">
                        <i class="far fa-bookmark"></i>
                    </button>
                </div>
                <div class="post-caption">
                    <span class="username">${post.user}</span> ${post.caption}
                </div>
            `;
            postsContainer.appendChild(postEl);
        });

        // Add like functionality
        this.setupLikeButtons();
    }

    setupLikeButtons() {
        document.querySelectorAll('.like-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const icon = btn.querySelector('i');
                const isLiked = icon.classList.contains('fas');
                
                if (isLiked) {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    btn.classList.remove('liked');
                } else {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    btn.classList.add('liked');
                    btn.classList.add('heart-beat');
                    
                    setTimeout(() => {
                        btn.classList.remove('heart-beat');
                    }, 600);
                }
            });
        });
    }

    loadTrending() {
        const trendingList = document.querySelector('.trending-list');
        const trending = [
            '#VYPXVibes',
            '#NeonLife',
            '#DarkArt',
            '#GoldenHour',
            '#MinimalFeeds'
        ];

        trending.forEach((tag, index) => {
            const trendingEl = document.createElement('div');
            trendingEl.className = 'trending-item stagger-item';
            trendingEl.innerHTML = `
                <i class="fas fa-hashtag" style="color: var(--golden-accent);"></i>
                <span>${tag}</span>
            `;
            trendingList.appendChild(trendingEl);
        });
    }

    loadVIPUsers() {
        const vipList = document.querySelector('.vip-list');
        const vipUsers = [
            { name: 'CyberQueen', avatar: 'C' },
            { name: 'NeonKing', avatar: 'N' },
            { name: 'GoldRush', avatar: 'G' },
            { name: 'VybeCheck', avatar: 'V' }
        ];

        vipUsers.forEach((user, index) => {
            const userEl = document.createElement('div');
            userEl.className = 'vip-user stagger-item';
            userEl.innerHTML = `
                <div class="vip-avatar">${user.avatar}</div>
                <span>${user.name}</span>
            `;
            vipList.appendChild(userEl);
        });
    }

    setupScrollAnimations() {
        // Smooth scroll for main content
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.add('smooth-scroll');
        }
    }

    initializeAnimations() {
        // Initialize GSAP animations
        gsap.registerPlugin();

        // Set initial states
        gsap.set('.fade-in-up', { opacity: 0, y: 30 });
        gsap.set('.stagger-item', { opacity: 0, y: 20 });
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.vypxApp = new VYPXApp();
});
