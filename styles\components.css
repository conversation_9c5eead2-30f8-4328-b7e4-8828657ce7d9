/* VYPX - Component Styles */

/* Auth Page */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 50%);
}

.auth-card {
    width: 100%;
    max-width: 400px;
    padding: 40px;
    text-align: center;
}

.auth-header .tagline {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 30px;
    font-weight: 300;
}

.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: 5px;
}

.tab-btn {
    flex: 1;
    padding: 12px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-family: var(--font-primary);
    font-weight: 500;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.tab-btn.active {
    background: var(--golden-accent);
    color: var(--bg-primary);
    box-shadow: 0 5px 15px var(--golden-glow);
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

/* Main Layout */
.main-layout {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    height: 100vh;
    gap: 20px;
    padding: 20px;
}

/* Sidebar */
.sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.sidebar-header {
    margin-bottom: 30px;
    text-align: center;
}

.sidebar-header .logo {
    font-size: 2rem;
    margin-bottom: 0;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    transform: translateX(5px);
}

.nav-item.active {
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    color: var(--bg-primary);
    box-shadow: 0 5px 15px var(--golden-glow);
}

.nav-item i {
    font-size: 1.2rem;
    width: 20px;
}

/* Main Content */
.main-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    overflow-y: auto;
    height: calc(100vh - 40px);
}

/* Stories Section */
.stories-section {
    display: flex;
    gap: 15px;
    padding: 20px 0;
    border-bottom: 1px solid var(--glass-border);
    margin-bottom: 20px;
    overflow-x: auto;
}

.story-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 80px;
    cursor: pointer;
    transition: var(--transition);
}

.story-item:hover {
    transform: translateY(-5px);
}

.story-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.add-story .story-avatar {
    background: var(--glass-bg);
    border: 2px dashed var(--golden-accent);
}

.story-avatar i {
    font-size: 1.5rem;
    color: var(--bg-primary);
}

.add-story .story-avatar i {
    color: var(--golden-accent);
}

.story-item span {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
}

/* Posts Feed */
.posts-feed {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.post-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
}

.post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.post-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--bg-primary);
}

.post-user-info h4 {
    font-family: var(--font-primary);
    font-weight: 600;
    margin-bottom: 2px;
}

.post-user-info span {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.post-options {
    margin-left: auto;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.post-options:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--golden-accent);
}

.post-content {
    position: relative;
    overflow: hidden;
}

.post-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition);
}

.post-card:hover .post-image {
    transform: scale(1.05);
}

.post-actions {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.3rem;
    cursor: pointer;
    transition: var(--transition);
    padding: 5px;
}

.action-btn:hover {
    color: var(--golden-accent);
    transform: scale(1.1);
}

.action-btn.liked {
    color: #ff3040;
}

.post-caption {
    padding: 0 20px 20px;
    color: var(--text-primary);
    line-height: 1.5;
}

.post-caption .username {
    font-weight: 600;
    color: var(--golden-accent);
}

/* Right Sidebar */
.right-sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.trending-section,
.vip-users-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 20px;
}

.trending-section h3,
.vip-users-section h3 {
    font-family: var(--font-primary);
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--golden-accent);
}

.trending-item,
.vip-user {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.trending-item:hover,
.vip-user:hover {
    background: rgba(255, 255, 255, 0.05);
}

.vip-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--bg-primary);
    font-size: 0.9rem;
}

/* Input Validation States */
.input-group.valid input {
    border-color: #4CAF50;
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

.input-group.invalid input {
    border-color: #FF5252;
    box-shadow: 0 0 15px rgba(255, 82, 82, 0.3);
}

/* Post Stats */
.post-stats {
    display: flex;
    gap: 15px;
    padding: 0 20px 10px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.post-stats span {
    font-weight: 500;
}

/* Loading Skeleton */
.loading-skeleton {
    padding: 20px 0;
}

.skeleton-post {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin-bottom: 20px;
}

.skeleton-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-user-info {
    flex: 1;
}

.skeleton-line {
    height: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
}

.skeleton-image {
    width: 100%;
    height: 300px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
}

.skeleton-actions {
    display: flex;
    gap: 20px;
}

.skeleton-action {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

/* Success Icon */
.success-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--golden-accent), #4CAF50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    z-index: 10;
}

/* Loading Spinner Small */
.loading-spinner-small {
    display: inline-block;
    margin-left: 10px;
}

.golden-ring-small {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid var(--bg-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    z-index: 10000;
    min-width: 300px;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-primary);
}

.notification-success {
    border-left: 4px solid #4CAF50;
}

.notification-error {
    border-left: 4px solid #FF5252;
}

.notification-warning {
    border-left: 4px solid #FFA726;
}

.notification-info {
    border-left: 4px solid var(--golden-accent);
}

/* Create Page */
.create-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.create-header {
    text-align: center;
    margin-bottom: 40px;
}

.create-header h2 {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    color: var(--golden-accent);
    margin-bottom: 10px;
}

.create-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.create-content {
    display: grid;
    gap: 30px;
}

.upload-section {
    padding: 30px;
}

.upload-area {
    border: 2px dashed var(--golden-accent);
    border-radius: var(--border-radius-lg);
    padding: 60px 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: #FFA500;
    background: rgba(255, 215, 0, 0.05);
}

.upload-placeholder i {
    font-size: 4rem;
    color: var(--golden-accent);
    margin-bottom: 20px;
    display: block;
}

.upload-placeholder h3 {
    font-family: var(--font-primary);
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.upload-placeholder p {
    color: var(--text-secondary);
}

.media-preview {
    text-align: center;
}

.preview-container {
    margin-bottom: 20px;
}

.preview-item {
    max-width: 100%;
    max-height: 400px;
    border-radius: var(--border-radius);
    object-fit: cover;
}

.change-media-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.change-media-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.post-details {
    padding: 30px;
}

.caption-section {
    margin-bottom: 30px;
}

.caption-section label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--golden-accent);
}

.caption-section textarea {
    width: 100%;
    min-height: 120px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 15px;
    color: var(--text-primary);
    font-family: var(--font-primary);
    resize: vertical;
    transition: var(--transition);
}

.caption-section textarea:focus {
    outline: none;
    border-color: var(--golden-accent);
    box-shadow: 0 0 20px var(--golden-glow);
}

.caption-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.word-count {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.caption-actions {
    display: flex;
    gap: 10px;
}

.tool-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.tool-btn:hover {
    color: var(--golden-accent);
    background: rgba(255, 215, 0, 0.1);
}

.post-settings {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setting-item label {
    font-weight: 500;
    color: var(--text-primary);
}

.setting-item select {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    transition: var(--transition);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background: var(--text-secondary);
    border-radius: 50%;
    transition: var(--transition);
}

input:checked + .toggle-slider {
    background: var(--golden-accent);
    border-color: var(--golden-accent);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
    background: var(--bg-primary);
}

.post-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.secondary-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-family: var(--font-primary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.secondary-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Explore Page */
.explore-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.explore-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.explore-header h2 {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    color: var(--golden-accent);
}

.search-bar {
    position: relative;
    width: 300px;
}

.search-bar i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--golden-accent);
}

.search-bar input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--text-primary);
    font-family: var(--font-primary);
    transition: var(--transition);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--golden-accent);
    box-shadow: 0 0 20px var(--golden-glow);
}

.vibe-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.filter-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    padding: 10px 20px;
    border-radius: var(--border-radius-lg);
    font-family: var(--font-primary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.filter-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.filter-btn.active {
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    color: var(--bg-primary);
    border-color: var(--golden-accent);
    box-shadow: 0 5px 15px var(--golden-glow);
}

.explore-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.explore-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
}

.explore-item:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.explore-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.explore-item:hover img {
    transform: scale(1.1);
}

.explore-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.8));
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 20px;
    opacity: 0;
    transition: var(--transition);
}

.explore-item:hover .explore-overlay {
    opacity: 1;
}

.explore-stats {
    display: flex;
    gap: 15px;
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
}

.explore-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Messages Page */
.messages-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    height: calc(100vh - 40px);
    gap: 20px;
    padding: 20px;
}

.chat-sidebar {
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chat-header h3 {
    font-family: var(--font-heading);
    color: var(--golden-accent);
    font-size: 1.5rem;
}

.new-chat-btn {
    background: var(--golden-accent);
    border: none;
    color: var(--bg-primary);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.new-chat-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px var(--golden-glow);
}

.chat-search {
    position: relative;
    margin-bottom: 20px;
}

.chat-search i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--golden-accent);
}

.chat-search input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    color: var(--text-primary);
    font-family: var(--font-primary);
}

.chat-list {
    flex: 1;
    overflow-y: auto;
}

.chat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 5px;
}

.chat-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.chat-item.active {
    background: rgba(255, 215, 0, 0.1);
    border-left: 3px solid var(--golden-accent);
}

.chat-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--bg-primary);
    position: relative;
}

.chat-avatar.online::after {
    content: '';
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border: 2px solid var(--bg-primary);
    border-radius: 50%;
}

.chat-info {
    flex: 1;
    min-width: 0;
}

.chat-info h4 {
    font-family: var(--font-primary);
    font-weight: 600;
    margin-bottom: 2px;
    color: var(--text-primary);
}

.chat-preview {
    color: var(--text-muted);
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-meta {
    text-align: right;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.unread-badge {
    background: var(--golden-accent);
    color: var(--bg-primary);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    margin-top: 5px;
}

.chat-main {
    display: flex;
    flex-direction: column;
    padding: 0;
    overflow: hidden;
}

.chat-welcome {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.welcome-content i {
    font-size: 4rem;
    color: var(--golden-accent);
    margin-bottom: 20px;
    display: block;
}

.welcome-content h3 {
    font-family: var(--font-heading);
    color: var(--text-primary);
    margin-bottom: 10px;
}

.welcome-content p {
    color: var(--text-secondary);
}

.chat-conversation {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--glass-border);
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.chat-details h4 {
    font-family: var(--font-primary);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.online-status {
    color: #4CAF50;
    font-size: 0.9rem;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.chat-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.chat-action-btn:hover {
    color: var(--golden-accent);
    background: rgba(255, 215, 0, 0.1);
}

.messages-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    display: flex;
    gap: 10px;
    max-width: 70%;
}

.message.sent {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    color: var(--bg-primary);
    flex-shrink: 0;
}

.message-content {
    background: var(--glass-bg);
    padding: 12px 16px;
    border-radius: 18px;
    color: var(--text-primary);
    word-wrap: break-word;
}

.message.sent .message-content {
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    color: var(--bg-primary);
}

.message-time {
    font-size: 0.7rem;
    color: var(--text-muted);
    margin-top: 5px;
    text-align: center;
}

.message-input-container {
    padding: 20px;
    border-top: 1px solid var(--glass-border);
}

.message-input {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    padding: 8px;
}

.input-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.input-action-btn:hover {
    color: var(--golden-accent);
    background: rgba(255, 215, 0, 0.1);
}

.message-input input {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-family: var(--font-primary);
    padding: 8px;
}

.message-input input:focus {
    outline: none;
}

.send-btn {
    padding: 8px 12px;
    font-size: 0.9rem;
}

/* Notifications Page */
.notifications-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.notifications-header h2 {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    color: var(--golden-accent);
}

.notification-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-family: var(--font-primary);
}

.mark-all-read:hover {
    color: var(--golden-accent);
}

.notification-settings {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.notification-settings:hover {
    color: var(--golden-accent);
    background: rgba(255, 215, 0, 0.1);
}

.notification-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    overflow-x: auto;
}

.notifications-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    cursor: pointer;
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.notification-item.unread {
    border-left: 4px solid var(--golden-accent);
    background: rgba(255, 215, 0, 0.05);
}

.notification-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--bg-primary);
    position: relative;
}

.notification-icon {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    background: var(--bg-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.notification-icon.like {
    color: #ff3040;
}

.notification-icon.comment {
    color: #4ECDC4;
}

.notification-icon.follow {
    color: var(--golden-accent);
}

.notification-content {
    flex: 1;
}

.notification-text {
    color: var(--text-primary);
    margin-bottom: 5px;
}

.notification-text .username {
    font-weight: 600;
    color: var(--golden-accent);
}

.notification-time {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.notification-media {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
    object-fit: cover;
}

/* Profile Page */
.profile-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.profile-header {
    margin-bottom: 30px;
    overflow: hidden;
    position: relative;
}

.profile-banner {
    height: 200px;
    background: linear-gradient(135deg, var(--golden-accent), #FFA500, #FF6B6B);
    position: relative;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), transparent);
}

.profile-info {
    padding: 30px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 30px;
    align-items: start;
    position: relative;
    margin-top: -50px;
}

.profile-avatar-container {
    position: relative;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--golden-accent), #FFA500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: bold;
    color: var(--bg-primary);
    border: 4px solid var(--bg-primary);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.edit-avatar-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
    background: var(--golden-accent);
    border: none;
    border-radius: 50%;
    color: var(--bg-primary);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.edit-avatar-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 20px var(--golden-glow);
}

.profile-details {
    padding-top: 20px;
}

.profile-name {
    font-family: var(--font-heading);
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 5px;
}

.profile-username {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.profile-bio {
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.profile-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--golden-accent);
    font-family: var(--font-heading);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.profile-actions {
    display: flex;
    gap: 15px;
    padding-top: 20px;
}

.profile-content {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.profile-tabs {
    display: flex;
    border-bottom: 1px solid var(--glass-border);
}

.profile-tabs .tab-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-family: var(--font-primary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 2px solid transparent;
}

.profile-tabs .tab-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
}

.profile-tabs .tab-btn.active {
    color: var(--golden-accent);
    border-bottom-color: var(--golden-accent);
    background: rgba(255, 215, 0, 0.05);
}

.profile-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2px;
    padding: 20px;
}

.profile-post {
    aspect-ratio: 1;
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.profile-post:hover {
    transform: scale(1.05);
}

.profile-post img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-post-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    opacity: 0;
    transition: var(--transition);
    color: white;
    font-weight: 500;
}

.profile-post:hover .profile-post-overlay {
    opacity: 1;
}

.profile-post-stat {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Particles */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--golden-accent);
    border-radius: 50%;
    pointer-events: none;
    box-shadow: 0 0 10px var(--golden-glow);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-layout {
        grid-template-columns: 200px 1fr 250px;
    }

    .messages-layout {
        grid-template-columns: 300px 1fr;
    }
}

@media (max-width: 768px) {
    .main-layout {
        grid-template-columns: 1fr;
        gap: 0;
        padding: 0;
    }

    .sidebar {
        display: none;
    }

    .main-content {
        border-radius: 0;
        height: 100vh;
    }

    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }

    .messages-layout {
        grid-template-columns: 1fr;
        padding: 10px;
    }

    .chat-sidebar {
        display: none;
    }

    .explore-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .search-bar {
        width: 100%;
    }

    .explore-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .create-container {
        padding: 20px 10px;
    }

    .create-content {
        gap: 20px;
    }

    .upload-area {
        padding: 40px 20px;
    }

    .upload-placeholder i {
        font-size: 3rem;
    }

    .post-actions {
        flex-direction: column;
    }

    .profile-info {
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
        margin-top: -30px;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }

    .profile-stats {
        justify-content: center;
    }

    .profile-actions {
        justify-content: center;
    }

    .profile-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1px;
        padding: 10px;
    }

    .notifications-container {
        padding: 10px;
    }

    .notification-filters {
        gap: 5px;
    }

    .filter-btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .vibe-filters {
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 30px 20px;
    }

    .logo {
        font-size: 2.5rem;
    }

    .stories-section {
        padding: 15px 0;
    }

    .story-avatar {
        width: 50px;
        height: 50px;
    }

    .post-card {
        margin: 0 -10px;
        border-radius: 0;
    }

    .explore-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .profile-avatar {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .profile-name {
        font-size: 1.5rem;
    }

    .profile-stats {
        gap: 20px;
    }

    .stat-number {
        font-size: 1.2rem;
    }
}
