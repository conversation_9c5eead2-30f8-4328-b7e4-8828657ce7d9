// VYPX - Animations Module

class AnimationManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupGSAPDefaults();
        this.setupScrollTriggers();
        this.setupHoverAnimations();
        this.setupPageTransitions();
    }

    setupGSAPDefaults() {
        // Set default GSAP settings
        gsap.defaults({
            duration: 0.6,
            ease: "power2.out"
        });

        // Custom eases
        gsap.registerEase("golden", "power2.inOut");
        gsap.registerEase("neon", "back.out(1.7)");
    }

    setupScrollTriggers() {
        // Animate elements on scroll
        gsap.utils.toArray('.fade-in-up').forEach(element => {
            gsap.fromTo(element, 
                { opacity: 0, y: 50 },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    scrollTrigger: {
                        trigger: element,
                        start: "top 80%",
                        end: "bottom 20%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        });

        // Stagger animations for lists
        gsap.utils.toArray('.stagger-container').forEach(container => {
            const items = container.querySelectorAll('.stagger-item');
            
            gsap.fromTo(items,
                { opacity: 0, y: 30 },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.5,
                    stagger: 0.1,
                    scrollTrigger: {
                        trigger: container,
                        start: "top 80%",
                        toggleActions: "play none none reverse"
                    }
                }
            );
        });
    }

    setupHoverAnimations() {
        // Glass card hover effects
        document.addEventListener('mouseenter', (e) => {
            if (e.target.matches('.glass-hover') || e.target.closest('.glass-hover')) {
                const element = e.target.matches('.glass-hover') ? e.target : e.target.closest('.glass-hover');
                this.animateGlassHover(element, true);
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.matches('.glass-hover') || e.target.closest('.glass-hover')) {
                const element = e.target.matches('.glass-hover') ? e.target : e.target.closest('.glass-hover');
                this.animateGlassHover(element, false);
            }
        }, true);

        // Button hover effects
        document.addEventListener('mouseenter', (e) => {
            if (e.target.matches('.golden-btn')) {
                this.animateButtonHover(e.target, true);
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.matches('.golden-btn')) {
                this.animateButtonHover(e.target, false);
            }
        }, true);

        // Navigation item hover
        document.addEventListener('mouseenter', (e) => {
            if (e.target.matches('.nav-item') && !e.target.classList.contains('active')) {
                this.animateNavHover(e.target, true);
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.matches('.nav-item') && !e.target.classList.contains('active')) {
                this.animateNavHover(e.target, false);
            }
        }, true);
    }

    animateGlassHover(element, isEnter) {
        if (isEnter) {
            gsap.to(element, {
                y: -5,
                scale: 1.02,
                boxShadow: "0 20px 40px rgba(255, 215, 0, 0.2)",
                backdropFilter: "blur(25px)",
                duration: 0.3
            });
        } else {
            gsap.to(element, {
                y: 0,
                scale: 1,
                boxShadow: "0 10px 20px rgba(0, 0, 0, 0.1)",
                backdropFilter: "blur(20px)",
                duration: 0.3
            });
        }
    }

    animateButtonHover(button, isEnter) {
        if (isEnter) {
            gsap.to(button, {
                scale: 1.05,
                boxShadow: "0 15px 35px rgba(255, 215, 0, 0.4)",
                duration: 0.3
            });
            
            // Animate glow effect
            const glow = button.querySelector('.btn-glow');
            if (glow) {
                gsap.to(glow, {
                    left: "100%",
                    duration: 0.5
                });
            }
        } else {
            gsap.to(button, {
                scale: 1,
                boxShadow: "0 10px 30px rgba(255, 215, 0, 0.3)",
                duration: 0.3
            });
            
            const glow = button.querySelector('.btn-glow');
            if (glow) {
                gsap.set(glow, { left: "-100%" });
            }
        }
    }

    animateNavHover(navItem, isEnter) {
        if (isEnter) {
            gsap.to(navItem, {
                x: 10,
                backgroundColor: "rgba(255, 255, 255, 0.05)",
                color: "var(--text-primary)",
                duration: 0.3
            });
        } else {
            gsap.to(navItem, {
                x: 0,
                backgroundColor: "transparent",
                color: "var(--text-secondary)",
                duration: 0.3
            });
        }
    }

    setupPageTransitions() {
        // Page transition animations
        this.pageTransitionTimeline = gsap.timeline({ paused: true });
    }

    animatePageTransition(fromPage, toPage, direction = 'forward') {
        const tl = gsap.timeline();
        
        if (direction === 'forward') {
            tl.to(fromPage, {
                opacity: 0,
                x: -100,
                duration: 0.4,
                ease: "power2.in"
            })
            .set(toPage, { x: 100, opacity: 0 })
            .to(toPage, {
                opacity: 1,
                x: 0,
                duration: 0.4,
                ease: "power2.out"
            });
        } else {
            tl.to(fromPage, {
                opacity: 0,
                x: 100,
                duration: 0.4,
                ease: "power2.in"
            })
            .set(toPage, { x: -100, opacity: 0 })
            .to(toPage, {
                opacity: 1,
                x: 0,
                duration: 0.4,
                ease: "power2.out"
            });
        }
        
        return tl;
    }

    // Notification animations
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        gsap.fromTo(notification,
            { opacity: 0, y: -50, scale: 0.8 },
            { opacity: 1, y: 0, scale: 1, duration: 0.5, ease: "back.out(1.7)" }
        );
        
        // Auto remove
        setTimeout(() => {
            gsap.to(notification, {
                opacity: 0,
                y: -50,
                scale: 0.8,
                duration: 0.3,
                onComplete: () => notification.remove()
            });
        }, 3000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || icons.info;
    }

    // Loading animations
    showLoadingSpinner(container) {
        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner-overlay';
        spinner.innerHTML = `
            <div class="golden-ring"></div>
            <div class="loading-text">Loading...</div>
        `;
        
        container.appendChild(spinner);
        
        gsap.fromTo(spinner,
            { opacity: 0 },
            { opacity: 1, duration: 0.3 }
        );
        
        return spinner;
    }

    hideLoadingSpinner(spinner) {
        gsap.to(spinner, {
            opacity: 0,
            duration: 0.3,
            onComplete: () => spinner.remove()
        });
    }

    // Parallax effect for backgrounds
    setupParallax() {
        document.addEventListener('mousemove', (e) => {
            const { clientX, clientY } = e;
            const { innerWidth, innerHeight } = window;
            
            const xPercent = (clientX / innerWidth - 0.5) * 2;
            const yPercent = (clientY / innerHeight - 0.5) * 2;
            
            gsap.to('.parallax-bg', {
                x: xPercent * 20,
                y: yPercent * 20,
                duration: 0.5,
                ease: "power2.out"
            });
        });
    }

    // Text typing animation
    typeText(element, text, speed = 50) {
        element.textContent = '';
        let i = 0;
        
        const typeInterval = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            
            if (i >= text.length) {
                clearInterval(typeInterval);
            }
        }, speed);
    }

    // Morphing shapes animation
    morphShape(element, newPath, duration = 1) {
        gsap.to(element, {
            attr: { d: newPath },
            duration: duration,
            ease: "power2.inOut"
        });
    }

    // Particle system for special effects
    createParticles(container, count = 20) {
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: var(--golden-accent);
                border-radius: 50%;
                pointer-events: none;
            `;
            
            container.appendChild(particle);
            
            gsap.set(particle, {
                x: Math.random() * container.offsetWidth,
                y: Math.random() * container.offsetHeight,
                opacity: 0
            });
            
            gsap.to(particle, {
                opacity: 1,
                y: "-=100",
                x: `+=${Math.random() * 100 - 50}`,
                duration: 2 + Math.random() * 2,
                ease: "power2.out",
                onComplete: () => particle.remove()
            });
        }
    }
}

// Initialize animation manager
document.addEventListener('DOMContentLoaded', () => {
    window.animationManager = new AnimationManager();
});
