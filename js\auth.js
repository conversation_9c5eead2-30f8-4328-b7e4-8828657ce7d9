// VYPX - Authentication Module

class AuthManager {
    constructor() {
        this.isAuthenticated = false;
        this.currentUser = null;
        this.init();
    }

    init() {
        this.setupAuthAnimations();
        this.setupFormValidation();
    }

    setupAuthAnimations() {
        // Animate auth card on load
        gsap.fromTo('.auth-card', 
            { 
                opacity: 0, 
                y: 50, 
                scale: 0.9 
            },
            { 
                opacity: 1, 
                y: 0, 
                scale: 1, 
                duration: 0.8, 
                ease: "back.out(1.7)",
                delay: 0.5
            }
        );

        // Animate input focus effects
        document.querySelectorAll('.input-group input').forEach(input => {
            input.addEventListener('focus', () => {
                gsap.to(input.parentElement, {
                    scale: 1.02,
                    duration: 0.2
                });
                
                gsap.to(input.parentElement.querySelector('i'), {
                    color: '#FFD700',
                    scale: 1.1,
                    duration: 0.2
                });
            });

            input.addEventListener('blur', () => {
                gsap.to(input.parentElement, {
                    scale: 1,
                    duration: 0.2
                });
                
                gsap.to(input.parentElement.querySelector('i'), {
                    color: '#FFD700',
                    scale: 1,
                    duration: 0.2
                });
            });
        });

        // Animate button hover effects
        document.querySelectorAll('.golden-btn').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                gsap.to(btn, {
                    scale: 1.05,
                    boxShadow: '0 15px 35px rgba(255, 215, 0, 0.4)',
                    duration: 0.3
                });
            });

            btn.addEventListener('mouseleave', () => {
                gsap.to(btn, {
                    scale: 1,
                    boxShadow: '0 10px 30px rgba(255, 215, 0, 0.3)',
                    duration: 0.3
                });
            });
        });
    }

    setupFormValidation() {
        // Real-time validation
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('input', () => {
                this.validateField(input);
            });
        });
    }

    validateField(input) {
        const value = input.value.trim();
        const type = input.type;
        const inputGroup = input.parentElement;
        
        // Remove existing validation classes
        inputGroup.classList.remove('valid', 'invalid');
        
        let isValid = false;
        
        switch(type) {
            case 'email':
                isValid = this.validateEmail(value);
                break;
            case 'password':
                isValid = this.validatePassword(value);
                break;
            case 'text':
                if (input.placeholder.includes('Username')) {
                    isValid = this.validateUsername(value);
                } else {
                    isValid = value.length >= 2;
                }
                break;
            default:
                isValid = value.length > 0;
        }
        
        if (value.length > 0) {
            inputGroup.classList.add(isValid ? 'valid' : 'invalid');
            
            // Animate validation feedback
            const icon = inputGroup.querySelector('i');
            if (isValid) {
                gsap.to(icon, {
                    color: '#4CAF50',
                    scale: 1.1,
                    duration: 0.2
                });
            } else {
                gsap.to(icon, {
                    color: '#FF5252',
                    scale: 1.1,
                    duration: 0.2
                });
            }
        }
        
        return isValid;
    }

    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    validatePassword(password) {
        return password.length >= 6;
    }

    validateUsername(username) {
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        return usernameRegex.test(username);
    }

    async login(credentials) {
        try {
            // Show loading animation
            this.showLoadingState('login');
            
            // Simulate API call
            await this.simulateAPICall();
            
            // Create user session
            this.currentUser = {
                id: 1,
                username: credentials.username || 'VYPXUser',
                email: credentials.email || '<EMAIL>',
                avatar: credentials.username ? credentials.username[0].toUpperCase() : 'V',
                joinDate: new Date(),
                followers: Math.floor(Math.random() * 10000),
                following: Math.floor(Math.random() * 1000),
                posts: Math.floor(Math.random() * 500)
            };
            
            this.isAuthenticated = true;
            
            // Store in localStorage for persistence
            localStorage.setItem('vypx_user', JSON.stringify(this.currentUser));
            localStorage.setItem('vypx_auth', 'true');
            
            // Success animation
            this.showSuccessAnimation();
            
            return { success: true, user: this.currentUser };
            
        } catch (error) {
            this.showErrorAnimation();
            return { success: false, error: error.message };
        }
    }

    async signup(userData) {
        try {
            // Show loading animation
            this.showLoadingState('signup');
            
            // Simulate API call
            await this.simulateAPICall();
            
            // Create new user
            this.currentUser = {
                id: Date.now(),
                username: userData.username,
                email: userData.email,
                fullName: userData.fullName,
                avatar: userData.username[0].toUpperCase(),
                joinDate: new Date(),
                followers: 0,
                following: 0,
                posts: 0
            };
            
            this.isAuthenticated = true;
            
            // Store in localStorage
            localStorage.setItem('vypx_user', JSON.stringify(this.currentUser));
            localStorage.setItem('vypx_auth', 'true');
            
            // Success animation
            this.showSuccessAnimation();
            
            return { success: true, user: this.currentUser };
            
        } catch (error) {
            this.showErrorAnimation();
            return { success: false, error: error.message };
        }
    }

    logout() {
        this.isAuthenticated = false;
        this.currentUser = null;
        
        // Clear localStorage
        localStorage.removeItem('vypx_user');
        localStorage.removeItem('vypx_auth');
        
        // Redirect to auth page
        if (window.vypxApp) {
            window.vypxApp.navigateToPage('auth');
        }
    }

    checkAuthStatus() {
        const authStatus = localStorage.getItem('vypx_auth');
        const userData = localStorage.getItem('vypx_user');
        
        if (authStatus === 'true' && userData) {
            this.isAuthenticated = true;
            this.currentUser = JSON.parse(userData);
            return true;
        }
        
        return false;
    }

    showLoadingState(formType) {
        const form = document.getElementById(`${formType}-form`);
        const button = form.querySelector('.golden-btn');
        const originalText = button.querySelector('span').textContent;
        
        button.disabled = true;
        button.querySelector('span').textContent = formType === 'login' ? 'Entering VYBE...' : 'Creating Account...';
        
        // Add loading spinner
        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner-small';
        spinner.innerHTML = '<div class="golden-ring-small"></div>';
        button.appendChild(spinner);
        
        // Animate button
        gsap.to(button, {
            scale: 0.95,
            duration: 0.1
        });
    }

    showSuccessAnimation() {
        // Create success checkmark
        const successIcon = document.createElement('div');
        successIcon.className = 'success-icon';
        successIcon.innerHTML = '<i class="fas fa-check"></i>';
        
        document.querySelector('.auth-card').appendChild(successIcon);
        
        // Animate success
        gsap.fromTo(successIcon, 
            { scale: 0, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.5, ease: "back.out(1.7)" }
        );
        
        // Fade out auth card
        setTimeout(() => {
            gsap.to('.auth-card', {
                opacity: 0,
                scale: 0.9,
                duration: 0.5,
                onComplete: () => {
                    if (window.vypxApp) {
                        window.vypxApp.navigateToPage('home');
                    }
                }
            });
        }, 1000);
    }

    showErrorAnimation() {
        const authCard = document.querySelector('.auth-card');
        
        // Shake animation
        gsap.to(authCard, {
            x: -10,
            duration: 0.1,
            yoyo: true,
            repeat: 5,
            ease: "power2.inOut",
            onComplete: () => {
                gsap.set(authCard, { x: 0 });
            }
        });
        
        // Reset button state
        document.querySelectorAll('.golden-btn').forEach(btn => {
            btn.disabled = false;
            const spinner = btn.querySelector('.loading-spinner-small');
            if (spinner) spinner.remove();
        });
    }

    simulateAPICall() {
        return new Promise((resolve) => {
            setTimeout(resolve, 1500);
        });
    }
}

// Initialize auth manager
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
    
    // Check if user is already logged in
    if (window.authManager.checkAuthStatus()) {
        setTimeout(() => {
            if (window.vypxApp) {
                window.vypxApp.isLoggedIn = true;
                window.vypxApp.navigateToPage('home');
            }
        }, 2500);
    }
});
