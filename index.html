<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VYPX - Join the VYBE</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&family=Bebas+Neue&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="golden-ring"></div>
            <div class="loading-text">VYPX</div>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app" class="app-container">
        
        <!-- Login/Signup Page -->
        <div id="auth-page" class="page active">
            <div class="auth-container">
                <div class="auth-card glass-card">
                    <div class="auth-header">
                        <h1 class="logo">VYPX</h1>
                        <p class="tagline">Join the VYBE</p>
                    </div>
                    
                    <div class="auth-tabs">
                        <button class="tab-btn active" data-tab="login">Login</button>
                        <button class="tab-btn" data-tab="signup">Sign Up</button>
                    </div>

                    <div class="auth-forms">
                        <!-- Login Form -->
                        <form id="login-form" class="auth-form active">
                            <div class="input-group">
                                <i class="fas fa-user"></i>
                                <input type="text" placeholder="Username or Email" required>
                            </div>
                            <div class="input-group">
                                <i class="fas fa-lock"></i>
                                <input type="password" placeholder="Password" required>
                            </div>
                            <button type="submit" class="golden-btn">
                                <span>Enter the VYBE</span>
                                <div class="btn-glow"></div>
                            </button>
                        </form>

                        <!-- Signup Form -->
                        <form id="signup-form" class="auth-form">
                            <div class="input-group">
                                <i class="fas fa-user"></i>
                                <input type="text" placeholder="Full Name" required>
                            </div>
                            <div class="input-group">
                                <i class="fas fa-envelope"></i>
                                <input type="email" placeholder="Email" required>
                            </div>
                            <div class="input-group">
                                <i class="fas fa-at"></i>
                                <input type="text" placeholder="Username" required>
                            </div>
                            <div class="input-group">
                                <i class="fas fa-lock"></i>
                                <input type="password" placeholder="Password" required>
                            </div>
                            <button type="submit" class="golden-btn">
                                <span>Create Account</span>
                                <div class="btn-glow"></div>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Home Feed Page -->
        <div id="home-page" class="page">
            <div class="main-layout">
                <!-- Left Sidebar -->
                <aside class="sidebar left-sidebar">
                    <div class="sidebar-header">
                        <h2 class="logo">VYPX</h2>
                    </div>
                    <nav class="sidebar-nav">
                        <a href="#" class="nav-item active" data-page="home">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <a href="#" class="nav-item" data-page="explore">
                            <i class="fas fa-compass"></i>
                            <span>Explore</span>
                        </a>
                        <a href="#" class="nav-item" data-page="messages">
                            <i class="fas fa-paper-plane"></i>
                            <span>Messages</span>
                        </a>
                        <a href="#" class="nav-item" data-page="notifications">
                            <i class="fas fa-bell"></i>
                            <span>Notifications</span>
                        </a>
                        <a href="#" class="nav-item" data-page="create">
                            <i class="fas fa-plus-square"></i>
                            <span>Create</span>
                        </a>
                        <a href="#" class="nav-item" data-page="profile">
                            <i class="fas fa-user-circle"></i>
                            <span>Profile</span>
                        </a>
                    </nav>
                </aside>

                <!-- Main Content -->
                <main class="main-content">
                    <div class="feed-container">
                        <div class="stories-section">
                            <div class="story-item add-story">
                                <div class="story-avatar">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <span>Your Story</span>
                            </div>
                            <!-- Story items will be dynamically added -->
                        </div>
                        
                        <div class="posts-feed" id="posts-feed">
                            <!-- Posts will be dynamically loaded -->
                        </div>
                    </div>
                </main>

                <!-- Right Sidebar -->
                <aside class="sidebar right-sidebar">
                    <div class="trending-section">
                        <h3>Trending Vibes</h3>
                        <div class="trending-list">
                            <!-- Trending items will be dynamically added -->
                        </div>
                    </div>
                    
                    <div class="vip-users-section">
                        <h3>VIP Users</h3>
                        <div class="vip-list">
                            <!-- VIP users will be dynamically added -->
                        </div>
                    </div>
                </aside>
            </div>
        </div>

        <!-- Create Post Page -->
        <div id="create-page" class="page">
            <div class="create-container">
                <div class="create-header">
                    <h2>Create New Post</h2>
                    <p>Share your vibe with the VYPX community</p>
                </div>

                <div class="create-content">
                    <div class="upload-section glass-card">
                        <div class="upload-area" id="upload-area">
                            <div class="upload-placeholder">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <h3>Drag & Drop or Click to Upload</h3>
                                <p>Support: JPG, PNG, MP4, GIF (Max 10MB)</p>
                            </div>
                            <input type="file" id="file-input" accept="image/*,video/*" multiple hidden>
                        </div>

                        <div class="media-preview" id="media-preview" style="display: none;">
                            <div class="preview-container"></div>
                            <button class="change-media-btn">Change Media</button>
                        </div>
                    </div>

                    <div class="post-details glass-card">
                        <div class="caption-section">
                            <label>Caption</label>
                            <textarea id="post-caption" placeholder="Write a caption... #VYPX #Vibes" maxlength="2200"></textarea>
                            <div class="caption-tools">
                                <div class="word-count">
                                    <span id="char-count">0</span>/2200
                                </div>
                                <div class="caption-actions">
                                    <button class="tool-btn" id="emoji-btn">
                                        <i class="far fa-smile"></i>
                                    </button>
                                    <button class="tool-btn" id="hashtag-btn">
                                        <i class="fas fa-hashtag"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="post-settings">
                            <div class="setting-item">
                                <label>Visibility</label>
                                <select id="post-visibility">
                                    <option value="public">Public</option>
                                    <option value="followers">Followers Only</option>
                                    <option value="private">Private</option>
                                </select>
                            </div>

                            <div class="setting-item">
                                <label>Allow Comments</label>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="allow-comments" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="post-actions">
                            <button class="secondary-btn">Save Draft</button>
                            <button class="golden-btn" id="publish-btn">
                                <span>Share to VYPX</span>
                                <div class="btn-glow"></div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Explore Page -->
        <div id="explore-page" class="page">
            <div class="explore-container">
                <div class="explore-header">
                    <h2>Explore</h2>
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search users, hashtags, vibes...">
                    </div>
                </div>

                <div class="vibe-filters">
                    <button class="filter-btn active" data-filter="all">All Vibes</button>
                    <button class="filter-btn" data-filter="dark-art">Dark Art</button>
                    <button class="filter-btn" data-filter="neon-life">Neon Life</button>
                    <button class="filter-btn" data-filter="minimal">Minimal Feeds</button>
                    <button class="filter-btn" data-filter="golden">Golden Hour</button>
                </div>

                <div class="explore-grid" id="explore-grid">
                    <!-- Grid items will be dynamically loaded -->
                </div>
            </div>
        </div>

        <!-- Messages Page -->
        <div id="messages-page" class="page">
            <div class="messages-layout">
                <div class="chat-sidebar glass-card">
                    <div class="chat-header">
                        <h3>Messages</h3>
                        <button class="new-chat-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div class="chat-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search conversations...">
                    </div>
                    <div class="chat-list" id="chat-list">
                        <!-- Chat items will be dynamically loaded -->
                    </div>
                </div>

                <div class="chat-main glass-card">
                    <div class="chat-welcome" id="chat-welcome">
                        <div class="welcome-content">
                            <i class="fas fa-comments"></i>
                            <h3>Select a conversation</h3>
                            <p>Choose from your existing conversations or start a new one</p>
                        </div>
                    </div>

                    <div class="chat-conversation" id="chat-conversation" style="display: none;">
                        <div class="conversation-header">
                            <div class="chat-user-info">
                                <div class="chat-avatar">N</div>
                                <div class="chat-details">
                                    <h4>NeonVibes</h4>
                                    <span class="online-status">Online</span>
                                </div>
                            </div>
                            <div class="chat-actions">
                                <button class="chat-action-btn">
                                    <i class="fas fa-phone"></i>
                                </button>
                                <button class="chat-action-btn">
                                    <i class="fas fa-video"></i>
                                </button>
                                <button class="chat-action-btn">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </div>

                        <div class="messages-container" id="messages-container">
                            <!-- Messages will be dynamically loaded -->
                        </div>

                        <div class="message-input-container">
                            <div class="message-input">
                                <button class="input-action-btn">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <input type="text" placeholder="Type a message..." id="message-input">
                                <button class="input-action-btn">
                                    <i class="far fa-smile"></i>
                                </button>
                                <button class="send-btn golden-btn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications Page -->
        <div id="notifications-page" class="page">
            <div class="notifications-container">
                <div class="notifications-header">
                    <h2>Notifications</h2>
                    <div class="notification-actions">
                        <button class="mark-all-read">Mark all as read</button>
                        <button class="notification-settings">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>

                <div class="notification-filters">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="likes">Likes</button>
                    <button class="filter-btn" data-filter="comments">Comments</button>
                    <button class="filter-btn" data-filter="follows">Follows</button>
                    <button class="filter-btn" data-filter="mentions">Mentions</button>
                </div>

                <div class="notifications-list" id="notifications-list">
                    <!-- Notifications will be dynamically loaded -->
                </div>
            </div>
        </div>

        <!-- Profile Page -->
        <div id="profile-page" class="page">
            <div class="profile-container">
                <div class="profile-header glass-card">
                    <div class="profile-banner">
                        <div class="banner-overlay"></div>
                    </div>
                    <div class="profile-info">
                        <div class="profile-avatar-container">
                            <div class="profile-avatar">V</div>
                            <button class="edit-avatar-btn">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <div class="profile-details">
                            <h2 class="profile-name">VYPXUser</h2>
                            <p class="profile-username">@vypxuser</p>
                            <p class="profile-bio">Living the golden vibe ✨ | Digital creator | #VYPXLife</p>
                            <div class="profile-stats">
                                <div class="stat-item">
                                    <span class="stat-number">1.2K</span>
                                    <span class="stat-label">Posts</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">45.6K</span>
                                    <span class="stat-label">Followers</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">892</span>
                                    <span class="stat-label">Following</span>
                                </div>
                            </div>
                        </div>
                        <div class="profile-actions">
                            <button class="golden-btn">Edit Profile</button>
                            <button class="secondary-btn">Share Profile</button>
                        </div>
                    </div>
                </div>

                <div class="profile-content">
                    <div class="profile-tabs">
                        <button class="tab-btn active" data-tab="posts">
                            <i class="fas fa-th"></i>
                            <span>Posts</span>
                        </button>
                        <button class="tab-btn" data-tab="saved">
                            <i class="far fa-bookmark"></i>
                            <span>Saved</span>
                        </button>
                        <button class="tab-btn" data-tab="tagged">
                            <i class="fas fa-user-tag"></i>
                            <span>Tagged</span>
                        </button>
                    </div>

                    <div class="profile-grid" id="profile-grid">
                        <!-- Profile posts will be dynamically loaded -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/feed.js"></script>
    <script src="js/animations.js"></script>
</body>
</html>
