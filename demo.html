<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VYPX Demo - Social Media Platform</title>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: #0A0A0A;
            color: #FFD700;
            margin: 0;
            padding: 40px;
            text-align: center;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }
        
        .feature h3 {
            color: #FFD700;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border: none;
            color: #0A0A0A;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 20px 10px;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }
        
        .tech-stack {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 215, 0, 0.2);
        }
        
        .tech-stack h3 {
            color: #FFD700;
            margin-bottom: 15px;
        }
        
        .tech-list {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .tech-item {
            background: rgba(255, 215, 0, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>VYPX</h1>
        <p style="font-size: 1.2rem; color: rgba(255, 255, 255, 0.8); margin-bottom: 30px;">
            A futuristic social media platform with golden neon aesthetics
        </p>
        
        <div class="features">
            <div class="feature">
                <h3>🔐 Authentication</h3>
                <p>Modern glass-card login/signup with golden neon effects and smooth animations</p>
            </div>
            
            <div class="feature">
                <h3>🏠 Home Feed</h3>
                <p>Instagram-like feed with stories, posts, infinite scroll, and interactive elements</p>
            </div>
            
            <div class="feature">
                <h3>📝 Create Posts</h3>
                <p>Drag & drop file upload, caption editor, and publishing with golden button effects</p>
            </div>
            
            <div class="feature">
                <h3>💬 Messages</h3>
                <p>Real-time chat interface with typing indicators and smooth message animations</p>
            </div>
            
            <div class="feature">
                <h3>🔔 Notifications</h3>
                <p>Interactive notification system with filtering and golden badge animations</p>
            </div>
            
            <div class="feature">
                <h3>👤 Profile</h3>
                <p>User profiles with stats, post grids, and tabbed content organization</p>
            </div>
            
            <div class="feature">
                <h3>🔍 Explore</h3>
                <p>Discover content with vibe filters (Dark Art, Neon Life, Minimal Feeds)</p>
            </div>
            
            <div class="feature">
                <h3>✨ Animations</h3>
                <p>GSAP-powered transitions, hover effects, and glassmorphism design</p>
            </div>
        </div>
        
        <a href="index.html" class="demo-btn">Launch VYPX Platform</a>
        <a href="#" class="demo-btn" onclick="showInstructions()">View Instructions</a>
        
        <div class="tech-stack">
            <h3>Technology Stack</h3>
            <div class="tech-list">
                <span class="tech-item">HTML5</span>
                <span class="tech-item">CSS3</span>
                <span class="tech-item">JavaScript ES6+</span>
                <span class="tech-item">GSAP Animations</span>
                <span class="tech-item">Glassmorphism</span>
                <span class="tech-item">Responsive Design</span>
            </div>
        </div>
    </div>
    
    <script>
        function showInstructions() {
            alert(`VYPX Platform Instructions:

1. Click "Launch VYPX Platform" to start
2. Use the Login/Signup forms (any credentials work for demo)
3. Navigate through different sections using the sidebar
4. Try creating a post, sending messages, and exploring content
5. All interactions are animated with GSAP
6. The platform is fully responsive

Features to explore:
• Home feed with infinite scroll
• Create posts with drag & drop upload
• Messages with real-time chat simulation
• Notifications with filtering
• Profile with post grid
• Explore page with vibe filters

Enjoy the golden neon experience! ✨`);
        }
    </script>
</body>
</html>
